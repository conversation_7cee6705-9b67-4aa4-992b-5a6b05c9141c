#!/usr/bin/env python3
"""
KedayaAPI 主入口文件
用于 Zeabur 部署和 Gunicorn 部署
"""

import sys
import os
import psutil
import signal

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def kill_process_on_port(port):
    """
    关闭占用指定端口的进程
    """
    try:
        for proc in psutil.process_iter():
            try:
                connections = proc.net_connections()
                for conn in connections:
                    if hasattr(conn, "laddr") and conn.laddr.port == port:
                        print(
                            f"发现端口 {port} 被进程 {proc.pid} ({proc.name()}) 占用，正在关闭..."
                        )
                        if sys.platform == "win32":
                            os.kill(proc.pid, signal.SIGTERM)
                        else:
                            os.kill(proc.pid, signal.SIGKILL)
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
    except Exception as e:
        print(f"关闭进程时出错: {str(e)}")
    return False


# 导入 FastAPI 应用实例，使其在模块级别可用（供 Gunicorn 使用）
try:
    from api.main import app

    print("✅ 成功导入 FastAPI 应用实例")
except Exception as e:
    print(f"❌ 导入 FastAPI 应用实例失败: {str(e)}")
    import traceback

    traceback.print_exc()
    # 创建一个简单的应用实例作为后备
    from fastapi import FastAPI

    app = FastAPI(title="KedayaAPI - Error", description="应用启动失败")

    @app.get("/")
    async def root():
        return {"error": "应用启动失败，请检查日志"}


# 直接运行时的入口点
if __name__ == "__main__":
    try:
        import uvicorn

        # 获取端口 - 处理 Zeabur 环境变量问题
        port_env = os.getenv("PORT", "8000")
        try:
            # 如果端口是变量形式（如 ${WEB_PORT}），使用默认端口
            if port_env.startswith("${") and port_env.endswith("}"):
                PORT = 8000
            else:
                PORT = int(port_env)
        except (ValueError, AttributeError):
            PORT = 8000
        kill_process_on_port(PORT)
        print(f"🚀 启动 KedayaAPI 服务器，端口: {PORT}")

        # 启动服务器（使用已经导入的 app 实例）
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=PORT,
        )
    except Exception as e:
        print(f"❌ 启动服务器失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
